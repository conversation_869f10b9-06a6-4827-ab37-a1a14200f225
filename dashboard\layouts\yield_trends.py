"""
Yield Trends Layout
"""

from dash import dcc, html
import dash_bootstrap_components as dbc


def create_yield_trends_layout():
    """Create the yield trends layout"""
    return dbc.Container([
        # Header section
        dbc.Row([
            dbc.Col([
                html.H1("Yield Trend Analysis", className="display-4 text-primary mb-0"),
                html.P("Advanced statistical analysis and trend monitoring for yield performance",
                      className="lead text-muted")
            ])
        ], className="mb-4"),

        # Advanced filters section
        dbc.Card([
            dbc.CardBody([
                dbc.Row([
                    dbc.Col([
                        html.Label("Site Selection", className="form-label"),
                        dcc.Dropdown(
                            id='trends-site-filter',
                            placeholder="All Sites",
                            multi=True,
                            className="mb-2"
                        )
                    ], width=3),
                    dbc.Col([
                        html.Label("Product Selection", className="form-label"),
                        dcc.Dropdown(
                            id='trends-product-filter',
                            placeholder="All Products",
                            multi=True,
                            className="mb-2"
                        )
                    ], width=3),
                    dbc.Col([
                        html.Label("Time Period", className="form-label"),
                        dcc.Dropdown(
                            id='trends-time-filter',
                            options=[
                                {'label': 'Last 30 days', 'value': 30},
                                {'label': 'Last 90 days', 'value': 90},
                                {'label': 'Last 180 days', 'value': 180},
                                {'label': 'Last 365 days', 'value': 365}
                            ],
                            value=90,
                            clearable=False,
                            className="mb-2"
                        )
                    ], width=2),
                    dbc.Col([
                        html.Label("Granularity", className="form-label"),
                        dcc.Dropdown(
                            id='trends-granularity-filter',
                            options=[
                                {'label': 'Daily', 'value': 'daily'},
                                {'label': 'Weekly', 'value': 'weekly'},
                                {'label': 'Monthly', 'value': 'monthly'}
                            ],
                            value='weekly',
                            clearable=False,
                            className="mb-2"
                        )
                    ], width=2),
                    dbc.Col([
                        html.Label("Chart Type", className="form-label"),
                        dcc.Dropdown(
                            id='trends-chart-type',
                            options=[
                                {'label': 'Line Chart', 'value': 'line'},
                                {'label': 'Box Plot', 'value': 'box'},
                                {'label': 'Scatter Plot', 'value': 'scatter'}
                            ],
                            value='line',
                            clearable=False,
                            className="mb-2"
                        )
                    ], width=2)
                ])
            ])
        ], className="mb-4"),

        # Main trend chart
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Yield Trend Over Time", className="mb-0"),
                        html.Small("Historical yield performance with trend analysis", className="text-muted")
                    ]),
                    dbc.CardBody([
                        dcc.Graph(
                            id='main-trend-chart',
                            config={'displayModeBar': True, 'displaylogo': False},
                            style={'height': '500px'}
                        )
                    ])
                ])
            ])
        ], className="mb-4"),

        # Statistical analysis section
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Control Limits Analysis", className="mb-0"),
                        html.Small("Statistical process control with 3-sigma limits", className="text-muted")
                    ]),
                    dbc.CardBody([
                        html.Div([
                            html.Label("Select Test Parameter", className="form-label"),
                            dcc.Dropdown(
                                id='test-name-selector',
                                placeholder="Select a test parameter",
                                className="mb-3"
                            )
                        ]),
                        dcc.Graph(
                            id='control-limits-chart',
                            config={'displayModeBar': True, 'displaylogo': False},
                            style={'height': '400px'}
                        )
                    ])
                ])
            ], width=8),
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Statistical Summary", className="mb-0")
                    ]),
                    dbc.CardBody([
                        html.Div(id="statistical-summary")
                    ])
                ], className="mb-3"),
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Trend Indicators", className="mb-0")
                    ]),
                    dbc.CardBody([
                        html.Div(id="trend-indicators")
                    ])
                ])
            ], width=4)
        ], className="mb-4"),

        # Comparative analysis section
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Site Comparison", className="mb-0"),
                        html.Small("Yield performance comparison across sites", className="text-muted")
                    ]),
                    dbc.CardBody([
                        dcc.Graph(
                            id='site-comparison-chart',
                            config={'displayModeBar': True, 'displaylogo': False},
                            style={'height': '350px'}
                        )
                    ])
                ])
            ], width=6),
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Product Comparison", className="mb-0"),
                        html.Small("Yield performance comparison across products", className="text-muted")
                    ]),
                    dbc.CardBody([
                        dcc.Graph(
                            id='product-comparison-chart',
                            config={'displayModeBar': True, 'displaylogo': False},
                            style={'height': '350px'}
                        )
                    ])
                ])
            ], width=6)
        ], className="mb-4"),

        # Correlation analysis section
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Correlation Matrix", className="mb-0"),
                        html.Small("Correlation between different test parameters", className="text-muted")
                    ]),
                    dbc.CardBody([
                        dcc.Graph(
                            id='correlation-matrix',
                            config={'displayModeBar': True, 'displaylogo': False},
                            style={'height': '400px'}
                        )
                    ])
                ])
            ], width=8),
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Outlier Detection", className="mb-0")
                    ]),
                    dbc.CardBody([
                        html.Div(id="outlier-analysis")
                    ])
                ])
            ], width=4)
        ])

    ], fluid=True)
