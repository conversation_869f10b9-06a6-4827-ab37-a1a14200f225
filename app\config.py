"""
Configuration settings for YieldDoc application
"""

import os
from typing import List, Optional
from pydantic import validator
from pydantic_settings import BaseSettings
from functools import lru_cache


class Settings(BaseSettings):
    """Application settings"""

    # Application
    PROJECT_NAME: str = "YieldDoc"
    PROJECT_VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    DEBUG: bool = False

    # Security
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    BCRYPT_ROUNDS: int = 12

    # Database
    DATABASE_URL: str
    DATABASE_HOST: str = "localhost"
    DATABASE_PORT: int = 5432
    DATABASE_NAME: str = "yielddoc"
    DATABASE_USER: str = "yielddoc_user"
    DATABASE_PASSWORD: str

    # Redis
    REDIS_URL: str = "redis://localhost:6379/0"
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0

    # CORS
    BACKEND_CORS_ORIGINS: List[str] = []

    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # Email
    SMTP_TLS: bool = True
    SMTP_PORT: Optional[int] = None
    SMTP_HOST: Optional[str] = None
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    EMAILS_FROM_EMAIL: Optional[str] = None
    EMAILS_FROM_NAME: Optional[str] = None

    # Celery
    CELERY_BROKER_URL: str = "redis://localhost:6379/1"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/1"

    # File Upload
    MAX_UPLOAD_SIZE: int = 100000000  # 100MB
    UPLOAD_FOLDER: str = "uploads"
    REPORTS_FOLDER: str = "reports"
    TEMP_FOLDER: str = "temp"

    # Dashboard
    DASH_HOST: str = "0.0.0.0"
    DASH_PORT: int = 8050
    DASH_DEBUG: bool = False

    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"

    # Data Processing
    BATCH_SIZE: int = 1000
    MAX_WORKERS: int = 4
    PROCESSING_TIMEOUT: int = 3600  # 1 hour

    # Report Generation
    REPORT_RETENTION_DAYS: int = 90
    AUTO_CLEANUP_ENABLED: bool = True

    # Security
    SESSION_TIMEOUT: int = 3600  # 1 hour

    # Monitoring
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090

    # Company
    COMPANY_NAME: str = "Your Semiconductor Company"
    COMPANY_LOGO_URL: Optional[str] = None
    TIMEZONE: str = "UTC"

    class Config:
        env_file = ".env"
        case_sensitive = True


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    return Settings()


# Global settings instance
settings = get_settings()
