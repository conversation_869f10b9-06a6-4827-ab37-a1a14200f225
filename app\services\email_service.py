"""
Email service for automated report delivery and notifications
"""

import os
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
import structlog

from jinja2 import Environment, FileSystemLoader
from sqlalchemy.orm import Session

from app.database import get_db
from app.models.report import Report
from app.models.user import User
from app.config import settings

logger = structlog.get_logger()


class EmailService:
    """Service for sending emails with reports and notifications"""

    def __init__(self):
        self.smtp_server = settings.SMTP_SERVER
        self.smtp_port = settings.SMTP_PORT
        self.smtp_username = settings.SMTP_USERNAME
        self.smtp_password = settings.SMTP_PASSWORD
        self.smtp_use_tls = settings.SMTP_USE_TLS
        self.from_email = settings.FROM_EMAIL
        self.from_name = settings.FROM_NAME

        # Set up Jinja2 for email templates
        template_dir = Path(__file__).parent.parent / "templates" / "email"
        template_dir.mkdir(parents=True, exist_ok=True)
        self.jinja_env = Environment(loader=FileSystemLoader(str(template_dir)))

    async def send_report_email(self, report_id: int) -> Dict[str, Any]:
        """
        Send report via email to configured recipients
        """
        logger.info("Starting report email delivery", report_id=report_id)

        try:
            # Get database session
            db = next(get_db())

            # Get report
            report = db.query(Report).filter(Report.id == report_id).first()
            if not report:
                raise ValueError(f"Report {report_id} not found")

            if not report.email_recipients:
                logger.info("No email recipients configured for report", report_id=report_id)
                return {'sent': False, 'reason': 'No recipients configured'}

            if not report.file_path or not os.path.exists(report.file_path):
                raise ValueError(f"Report file not found: {report.file_path}")

            # Get report creator
            creator = db.query(User).filter(User.id == report.generated_by).first()

            # Prepare email content
            subject = f"YieldDoc Report: {report.title}"

            # Generate HTML email content
            html_content = await self._generate_report_email_html(report, creator)

            # Generate plain text content
            text_content = await self._generate_report_email_text(report, creator)

            # Send email to each recipient
            sent_count = 0
            failed_recipients = []

            for recipient in report.email_recipients:
                try:
                    await self._send_email(
                        to_email=recipient,
                        subject=subject,
                        html_content=html_content,
                        text_content=text_content,
                        attachments=[report.file_path]
                    )
                    sent_count += 1
                    logger.info(
                        "Report email sent successfully",
                        report_id=report_id,
                        recipient=recipient
                    )
                except Exception as e:
                    logger.error(
                        "Failed to send report email",
                        report_id=report_id,
                        recipient=recipient,
                        error=str(e)
                    )
                    failed_recipients.append(recipient)

            result = {
                'sent': sent_count > 0,
                'sent_count': sent_count,
                'total_recipients': len(report.email_recipients),
                'failed_recipients': failed_recipients
            }

            logger.info(
                "Report email delivery completed",
                report_id=report_id,
                **result
            )

            return result

        except Exception as e:
            logger.error(
                "Report email delivery failed",
                report_id=report_id,
                error=str(e)
            )
            raise
