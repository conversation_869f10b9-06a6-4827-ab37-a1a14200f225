"""
Report schemas for API request/response models
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, validator

from app.models.report import ReportType, ReportStatus, ReportFormat


class ReportBase(BaseModel):
    """Base report schema"""
    report_name: str
    report_type: ReportType
    report_format: ReportFormat = ReportFormat.PDF
    title: str
    description: Optional[str] = None
    period_start: datetime
    period_end: datetime
    site_ids: Optional[List[int]] = None
    product_names: Optional[List[str]] = None
    filters: Optional[Dict[str, Any]] = None
    parameters: Optional[Dict[str, Any]] = None
    email_recipients: Optional[List[str]] = None
    is_scheduled: bool = False
    schedule_cron: Optional[str] = None
    next_run: Optional[datetime] = None


class ReportCreate(ReportBase):
    """Report creation schema"""
    template_id: Optional[int] = None

    @validator('period_end')
    def validate_period_end(cls, v, values):
        if 'period_start' in values and v <= values['period_start']:
            raise ValueError('Period end must be after period start')
        return v

    @validator('email_recipients')
    def validate_email_recipients(cls, v):
        if v:
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            for email in v:
                if not re.match(email_pattern, email):
                    raise ValueError(f'Invalid email address: {email}')
        return v

    @validator('schedule_cron')
    def validate_schedule_cron(cls, v, values):
        if v and not values.get('is_scheduled', False):
            raise ValueError('Schedule cron can only be set when is_scheduled is True')
        return v


class ReportUpdate(BaseModel):
    """Report update schema"""
    report_name: Optional[str] = None
    title: Optional[str] = None
    description: Optional[str] = None
    email_recipients: Optional[List[str]] = None
    is_scheduled: Optional[bool] = None
    schedule_cron: Optional[str] = None
    next_run: Optional[datetime] = None
    status: Optional[ReportStatus] = None


class Report(ReportBase):
    """Report response schema"""
    id: int
    status: ReportStatus
    generated_by: int
    generated_at: Optional[datetime] = None
    file_path: Optional[str] = None
    file_size: Optional[int] = None
    file_hash: Optional[str] = None
    total_lots: Optional[int] = None
    total_wafers: Optional[int] = None
    average_yield: Optional[float] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    created_at: datetime
    updated_at: Optional[datetime] = None
    template_id: Optional[int] = None

    class Config:
        from_attributes = True


class ReportTemplateBase(BaseModel):
    """Base report template schema"""
    template_name: str
    display_name: str
    description: Optional[str] = None
    report_type: ReportType
    default_format: ReportFormat = ReportFormat.PDF
    template_path: str
    sections: Optional[Dict[str, Any]] = None
    charts: Optional[Dict[str, Any]] = None
    tables: Optional[Dict[str, Any]] = None
    default_parameters: Optional[Dict[str, Any]] = None
    required_parameters: Optional[List[str]] = None
    is_schedulable: bool = True
    default_schedule: Optional[str] = None
    is_active: bool = True
    is_public: bool = False
    version: str = "1.0"


class ReportTemplateCreate(ReportTemplateBase):
    """Report template creation schema"""

    @validator('template_name')
    def validate_template_name(cls, v):
        if len(v) < 3:
            raise ValueError('Template name must be at least 3 characters long')
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('Template name can only contain alphanumeric characters, hyphens, and underscores')
        return v


class ReportTemplate(ReportTemplateBase):
    """Report template response schema"""
    id: int
    created_by: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class ReportSummary(BaseModel):
    """Report summary for dashboard"""
    total_reports: int
    pending_reports: int
    completed_reports: int
    failed_reports: int
    reports_today: int
    reports_this_week: int
    reports_this_month: int


class ReportSchedule(BaseModel):
    """Report schedule information"""
    report_id: int
    report_name: str
    report_type: ReportType
    schedule_cron: str
    next_run: datetime
    is_active: bool
