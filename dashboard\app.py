"""
YieldDoc Main Dashboard Application
"""

import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import dash
from dash import dcc, html, Input, Output, State, callback_context
import dash_bootstrap_components as dbc
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import requests
import structlog

from app.config import settings
from dashboard.layouts.main_dashboard import create_main_layout
from dashboard.layouts.yield_trends import create_yield_trends_layout
from dashboard.layouts.wafer_maps import create_wafer_maps_layout
from dashboard.components.charts import YieldCharts
from dashboard.components.tables import YieldTables

# Configure logging
logger = structlog.get_logger()

# Initialize Dash app
app = dash.Dash(
    __name__,
    external_stylesheets=[
        dbc.themes.BOOTSTRAP,
        dbc.icons.FONT_AWESOME,
        "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
    ],
    suppress_callback_exceptions=True,
    meta_tags=[
        {"name": "viewport", "content": "width=device-width, initial-scale=1"}
    ]
)

# Set app title
app.title = "YieldDoc - Semiconductor Yield Dashboard"

# API base URL
API_BASE_URL = f"http://localhost:8000{settings.API_V1_STR}"

# Initialize components
yield_charts = YieldCharts()
yield_tables = YieldTables()

# Define app layout with navigation
app.layout = dbc.Container([
    dcc.Location(id='url', refresh=False),

    # Navigation bar
    dbc.NavbarSimple(
        children=[
            dbc.NavItem(dbc.NavLink("Main Dashboard", href="/", active="exact")),
            dbc.NavItem(dbc.NavLink("Yield Trends", href="/trends", active="exact")),
            dbc.NavItem(dbc.NavLink("Wafer Maps", href="/wafer-maps", active="exact")),
            dbc.DropdownMenu(
                children=[
                    dbc.DropdownMenuItem("Reports", href="/reports"),
                    dbc.DropdownMenuItem("Settings", href="/settings"),
                    dbc.DropdownMenuItem(divider=True),
                    dbc.DropdownMenuItem("Help", href="/help"),
                ],
                nav=True,
                in_navbar=True,
                label="More",
            ),
        ],
        brand="YieldDoc",
        brand_href="/",
        color="primary",
        dark=True,
        className="mb-4"
    ),

    # Page content
    html.Div(id='page-content'),

    # Interval component for auto-refresh
    dcc.Interval(
        id='interval-component',
        interval=30*1000,  # Update every 30 seconds
        n_intervals=0
    ),

    # Store components for data caching
    dcc.Store(id='kpi-data-store'),
    dcc.Store(id='yield-trend-store'),
    dcc.Store(id='site-data-store'),

], fluid=True, className="px-0")


# Callback for page routing
@app.callback(
    Output('page-content', 'children'),
    [Input('url', 'pathname')]
)
def display_page(pathname):
    """Route to different pages based on URL"""
    if pathname == '/trends':
        return create_yield_trends_layout()
    elif pathname == '/wafer-maps':
        return create_wafer_maps_layout()
    elif pathname == '/reports':
        return html.Div([
            html.H2("Reports", className="text-center mb-4"),
            html.P("Reports functionality coming soon...", className="text-center")
        ])
    elif pathname == '/settings':
        return html.Div([
            html.H2("Settings", className="text-center mb-4"),
            html.P("Settings functionality coming soon...", className="text-center")
        ])
    else:  # Default to main dashboard
        return create_main_layout()


# Callback for KPI data loading
@app.callback(
    Output('kpi-data-store', 'data'),
    [Input('interval-component', 'n_intervals'),
     Input('site-filter', 'value'),
     Input('days-filter', 'value')],
    prevent_initial_call=False
)
def load_kpi_data(n_intervals, site_id, days):
    """Load KPI data from API"""
    try:
        params = {'days': days or 30}
        if site_id:
            params['site_id'] = site_id

        response = requests.get(f"{API_BASE_URL}/dashboard/kpis", params=params)
        if response.status_code == 200:
            return response.json()
        else:
            logger.error("Failed to load KPI data", status_code=response.status_code)
            return {}
    except Exception as e:
        logger.error("Error loading KPI data", error=str(e))
        return {}


# Callback for yield trend data loading
@app.callback(
    Output('yield-trend-store', 'data'),
    [Input('interval-component', 'n_intervals'),
     Input('site-filter', 'value'),
     Input('days-filter', 'value'),
     Input('granularity-filter', 'value')],
    prevent_initial_call=False
)
def load_yield_trend_data(n_intervals, site_id, days, granularity):
    """Load yield trend data from API"""
    try:
        params = {
            'days': days or 30,
            'granularity': granularity or 'daily'
        }
        if site_id:
            params['site_id'] = site_id

        response = requests.get(f"{API_BASE_URL}/dashboard/yield-trend", params=params)
        if response.status_code == 200:
            return response.json()
        else:
            logger.error("Failed to load yield trend data", status_code=response.status_code)
            return []
    except Exception as e:
        logger.error("Error loading yield trend data", error=str(e))
        return []


# Main KPI cards callback
@app.callback(
    [Output('total-lots-card', 'children'),
     Output('average-yield-card', 'children'),
     Output('yield-trend-card', 'children'),
     Output('anomalies-card', 'children')],
    [Input('kpi-data-store', 'data')],
    prevent_initial_call=False
)
def update_kpi_cards(kpi_data):
    """Update KPI cards with latest data"""
    if not kpi_data:
        return "0", "0.0%", "No Data", "0"

    # Total lots card
    total_lots = kpi_data.get('total_lots', 0)

    # Average yield card with trend indicator
    avg_yield = kpi_data.get('average_yield', 0.0)
    yield_trend = kpi_data.get('yield_trend', 'stable')

    trend_icon = {
        'improving': 'fas fa-arrow-up text-success',
        'declining': 'fas fa-arrow-down text-danger',
        'stable': 'fas fa-minus text-warning'
    }.get(yield_trend, 'fas fa-minus text-warning')

    yield_card_content = html.Div([
        html.H3(f"{avg_yield:.1f}%", className="mb-0"),
        html.Small([
            html.I(className=trend_icon),
            f" {yield_trend.title()}"
        ], className="text-muted")
    ])

    # Yield trend description
    trend_description = {
        'improving': 'Trending Up',
        'declining': 'Trending Down',
        'stable': 'Stable'
    }.get(yield_trend, 'Unknown')

    # Anomalies count
    anomalies = kpi_data.get('recent_anomalies', 0)

    return (
        str(total_lots),
        yield_card_content,
        trend_description,
        str(anomalies)
    )


# Yield trend chart callback
@app.callback(
    Output('yield-trend-chart', 'figure'),
    [Input('yield-trend-store', 'data')],
    prevent_initial_call=False
)
def update_yield_trend_chart(trend_data):
    """Update yield trend chart"""
    return yield_charts.create_yield_trend_chart(trend_data)


# Site performance chart callback
@app.callback(
    Output('site-performance-chart', 'figure'),
    [Input('kpi-data-store', 'data')],
    prevent_initial_call=False
)
def update_site_performance_chart(kpi_data):
    """Update site performance chart"""
    site_performance = kpi_data.get('site_performance', []) if kpi_data else []
    return yield_charts.create_site_performance_chart(site_performance)


# Top products chart callback
@app.callback(
    Output('top-products-chart', 'figure'),
    [Input('kpi-data-store', 'data')],
    prevent_initial_call=False
)
def update_top_products_chart(kpi_data):
    """Update top products chart"""
    top_products = kpi_data.get('top_products', []) if kpi_data else []
    return yield_charts.create_top_products_chart(top_products)


# Pareto analysis chart callback
@app.callback(
    Output('pareto-chart', 'figure'),
    [Input('site-filter', 'value'),
     Input('days-filter', 'value')],
    prevent_initial_call=False
)
def update_pareto_chart(site_id, days):
    """Update Pareto analysis chart"""
    try:
        params = {'days': days or 30}
        if site_id:
            params['site_id'] = site_id

        response = requests.get(f"{API_BASE_URL}/dashboard/pareto-analysis", params=params)
        if response.status_code == 200:
            pareto_data = response.json()
            return yield_charts.create_pareto_chart(pareto_data.get('bins', []))
        else:
            return yield_charts.create_empty_chart("Failed to load Pareto data")
    except Exception as e:
        logger.error("Error loading Pareto data", error=str(e))
        return yield_charts.create_empty_chart("Error loading data")


# Wafer map callback
@app.callback(
    Output('wafer-map-display', 'children'),
    [Input('wafer-selector', 'value')],
    prevent_initial_call=True
)
def update_wafer_map(yield_data_id):
    """Update wafer map display"""
    if not yield_data_id:
        return html.Div("Select a lot to view wafer map", className="text-center text-muted p-4")

    try:
        response = requests.get(f"{API_BASE_URL}/dashboard/wafer-map/{yield_data_id}")
        if response.status_code == 200:
            wafer_data = response.json()
            return yield_charts.create_wafer_map(wafer_data)
        else:
            return html.Div("Failed to load wafer map", className="text-center text-danger p-4")
    except Exception as e:
        logger.error("Error loading wafer map", error=str(e))
        return html.Div("Error loading wafer map", className="text-center text-danger p-4")


# Control limits chart callback
@app.callback(
    Output('control-limits-chart', 'figure'),
    [Input('test-name-selector', 'value'),
     Input('site-filter', 'value'),
     Input('days-filter', 'value')],
    prevent_initial_call=True
)
def update_control_limits_chart(test_name, site_id, days):
    """Update control limits chart"""
    if not test_name:
        return yield_charts.create_empty_chart("Select a test to view control limits")

    try:
        params = {
            'test_name': test_name,
            'days': days or 90
        }
        if site_id:
            params['site_id'] = site_id

        response = requests.get(f"{API_BASE_URL}/dashboard/control-limits", params=params)
        if response.status_code == 200:
            control_data = response.json()
            return yield_charts.create_control_limits_chart(control_data)
        else:
            return yield_charts.create_empty_chart("Failed to load control limits data")
    except Exception as e:
        logger.error("Error loading control limits data", error=str(e))
        return yield_charts.create_empty_chart("Error loading data")


# Anomalies table callback
@app.callback(
    Output('anomalies-table', 'children'),
    [Input('site-filter', 'value'),
     Input('interval-component', 'n_intervals')],
    prevent_initial_call=False
)
def update_anomalies_table(site_id, n_intervals):
    """Update anomalies table"""
    try:
        params = {'days': 7}  # Last 7 days
        if site_id:
            params['site_id'] = site_id

        response = requests.get(f"{API_BASE_URL}/dashboard/anomalies", params=params)
        if response.status_code == 200:
            anomalies_data = response.json()
            return yield_tables.create_anomalies_table(anomalies_data.get('anomalies', []))
        else:
            return html.Div("Failed to load anomalies data", className="text-center text-muted")
    except Exception as e:
        logger.error("Error loading anomalies data", error=str(e))
        return html.Div("Error loading anomalies data", className="text-center text-danger")


if __name__ == '__main__':
    app.run(
        host=settings.DASH_HOST,
        port=settings.DASH_PORT,
        debug=settings.DASH_DEBUG
    )
