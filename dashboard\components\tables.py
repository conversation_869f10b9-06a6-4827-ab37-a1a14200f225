"""
Table components for the dashboard
"""

from dash import html, dash_table
import dash_bootstrap_components as dbc
import pandas as pd
from typing import List, Dict, Any
from datetime import datetime


class YieldTables:
    """Class for creating yield-related tables"""

    def __init__(self):
        self.table_style = {
            'backgroundColor': 'white',
            'color': 'black',
            'fontFamily': 'Inter, sans-serif'
        }

        self.header_style = {
            'backgroundColor': '#f8f9fa',
            'fontWeight': 'bold',
            'textAlign': 'center'
        }

    def create_anomalies_table(self, anomalies_data: List[Dict[str, Any]]) -> html.Div:
        """Create anomalies table with highlighting"""
        if not anomalies_data:
            return html.Div([
                html.P("No anomalies detected in the selected time period.",
                      className="text-center text-muted p-3")
            ])

        # Prepare data for table
        table_data = []
        for anomaly in anomalies_data:
            table_data.append({
                'Lot ID': anomaly.get('lot_id', 'N/A'),
                'Product': anomaly.get('product_name', 'N/A'),
                'Site': anomaly.get('site_id', 'N/A'),
                'Yield (%)': f"{anomaly.get('yield_percent', 0):.1f}",
                'Quality Score': f"{anomaly.get('quality_score', 0):.2f}",
                'Test Date': datetime.fromisoformat(anomaly['test_start_time']).strftime('%Y-%m-%d %H:%M') if anomaly.get('test_start_time') else 'N/A',
                'Total Die': anomaly.get('total_die_count', 0),
                'Good Die': anomaly.get('good_die_count', 0)
            })

        df = pd.DataFrame(table_data)

        return dash_table.DataTable(
            data=df.to_dict('records'),
            columns=[
                {'name': 'Lot ID', 'id': 'Lot ID', 'type': 'text'},
                {'name': 'Product', 'id': 'Product', 'type': 'text'},
                {'name': 'Site', 'id': 'Site', 'type': 'numeric'},
                {'name': 'Yield (%)', 'id': 'Yield (%)', 'type': 'numeric'},
                {'name': 'Quality Score', 'id': 'Quality Score', 'type': 'numeric'},
                {'name': 'Test Date', 'id': 'Test Date', 'type': 'datetime'},
                {'name': 'Total Die', 'id': 'Total Die', 'type': 'numeric'},
                {'name': 'Good Die', 'id': 'Good Die', 'type': 'numeric'}
            ],
            style_table={'overflowX': 'auto'},
            style_cell=self.table_style,
            style_header=self.header_style,
            style_data_conditional=[
                {
                    'if': {'filter_query': '{Yield (%)} < 80'},
                    'backgroundColor': '#f8d7da',
                    'color': 'black',
                },
                {
                    'if': {'filter_query': '{Quality Score} < 0.5'},
                    'backgroundColor': '#fff3cd',
                    'color': 'black',
                }
            ],
            sort_action="native",
            filter_action="native",
            page_action="native",
            page_current=0,
            page_size=10
        )

    def create_wafer_details_table(self, wafer_data: Dict[str, Any]) -> html.Div:
        """Create detailed wafer information table"""
        if not wafer_data or not wafer_data.get('wafers'):
            return html.Div([
                html.P("No wafer data available.",
                      className="text-center text-muted p-3")
            ])

        # Prepare data for table
        table_data = []
        for wafer in wafer_data['wafers']:
            table_data.append({
                'Wafer ID': wafer.get('wafer_id', 'N/A'),
                'Yield (%)': f"{wafer.get('wafer_yield', 0):.1f}",
                'Total Die': wafer.get('total_die_count', 0),
                'Good Die': wafer.get('good_die_count', 0),
                'Die Size X (μm)': wafer.get('die_size_x', 0),
                'Die Size Y (μm)': wafer.get('die_size_y', 0),
                'Die Count X': wafer.get('total_die_x', 0),
                'Die Count Y': wafer.get('total_die_y', 0)
            })

        df = pd.DataFrame(table_data)

        return dash_table.DataTable(
            data=df.to_dict('records'),
            columns=[
                {'name': 'Wafer ID', 'id': 'Wafer ID', 'type': 'text'},
                {'name': 'Yield (%)', 'id': 'Yield (%)', 'type': 'numeric', 'format': {'specifier': '.1f'}},
                {'name': 'Total Die', 'id': 'Total Die', 'type': 'numeric'},
                {'name': 'Good Die', 'id': 'Good Die', 'type': 'numeric'},
                {'name': 'Die Size X (μm)', 'id': 'Die Size X (μm)', 'type': 'numeric'},
                {'name': 'Die Size Y (μm)', 'id': 'Die Size Y (μm)', 'type': 'numeric'},
                {'name': 'Die Count X', 'id': 'Die Count X', 'type': 'numeric'},
                {'name': 'Die Count Y', 'id': 'Die Count Y', 'type': 'numeric'}
            ],
            style_table={'overflowX': 'auto'},
            style_cell=self.table_style,
            style_header=self.header_style,
            style_data_conditional=[
                {
                    'if': {'filter_query': '{Yield (%)} < 70'},
                    'backgroundColor': '#f8d7da',
                    'color': 'black',
                },
                {
                    'if': {'filter_query': '{Yield (%)} >= 90'},
                    'backgroundColor': '#d1e7dd',
                    'color': 'black',
                }
            ],
            sort_action="native",
            page_action="native",
            page_current=0,
            page_size=10
        )

    def create_statistical_summary_table(self, stats_data: Dict[str, Any]) -> html.Div:
        """Create statistical summary table"""
        if not stats_data:
            return html.Div([
                html.P("No statistical data available.",
                      className="text-center text-muted p-3")
            ])

        # Create summary cards instead of table for better visual appeal
        return dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.H6("Mean", className="card-title text-muted"),
                        html.H4(f"{stats_data.get('mean', 0):.2f}", className="text-primary")
                    ])
                ])
            ], width=6, className="mb-2"),
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.H6("Std Dev", className="card-title text-muted"),
                        html.H4(f"{stats_data.get('std_dev', 0):.2f}", className="text-info")
                    ])
                ])
            ], width=6, className="mb-2"),
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.H6("UCL", className="card-title text-muted"),
                        html.H4(f"{stats_data.get('upper_control_limit', 0):.2f}", className="text-danger")
                    ])
                ])
            ], width=6, className="mb-2"),
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.H6("LCL", className="card-title text-muted"),
                        html.H4(f"{stats_data.get('lower_control_limit', 0):.2f}", className="text-danger")
                    ])
                ])
            ], width=6, className="mb-2"),
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.H6("Sample Size", className="card-title text-muted"),
                        html.H4(f"{stats_data.get('sample_size', 0)}", className="text-secondary")
                    ])
                ])
            ], width=12, className="mb-2")
        ])

    def create_trend_indicators_table(self, trend_data: Dict[str, Any]) -> html.Div:
        """Create trend indicators display"""
        if not trend_data:
            return html.Div([
                html.P("No trend data available.",
                      className="text-center text-muted p-3")
            ])

        # Determine trend status and color
        trend = trend_data.get('trend', 'unknown')
        slope = trend_data.get('slope', 0)
        correlation = trend_data.get('correlation', 0)
        volatility = trend_data.get('volatility', 0)

        trend_color = {
            'improving': 'success',
            'declining': 'danger',
            'stable': 'warning',
            'unknown': 'secondary'
        }.get(trend, 'secondary')

        return dbc.Row([
            dbc.Col([
                dbc.Alert([
                    html.H5([
                        html.I(className="fas fa-chart-line me-2"),
                        f"Trend: {trend.title()}"
                    ], className="alert-heading"),
                    html.P(f"Slope: {slope:.4f} per day", className="mb-1"),
                    html.P(f"Correlation: {correlation:.3f}", className="mb-1"),
                    html.P(f"Volatility: {volatility:.1f}%", className="mb-0")
                ], color=trend_color, className="mb-0")
            ])
        ])
