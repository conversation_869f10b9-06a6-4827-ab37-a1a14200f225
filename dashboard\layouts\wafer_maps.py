"""
Wafer Maps Layout
"""

from dash import dcc, html
import dash_bootstrap_components as dbc


def create_wafer_maps_layout():
    """Create the wafer maps layout"""
    return dbc.Container([
        # Header section
        dbc.Row([
            dbc.Col([
                html.H1("Wafer Map Analysis", className="display-4 text-primary mb-0"),
                html.P("Interactive wafer-level yield visualization and die-level analysis",
                      className="lead text-muted")
            ])
        ], className="mb-4"),

        # Controls section
        dbc.Card([
            dbc.CardBody([
                dbc.Row([
                    dbc.Col([
                        html.Label("Select Lot", className="form-label"),
                        dcc.Dropdown(
                            id='wafer-selector',
                            placeholder="Select a lot to view wafer maps",
                            clearable=True,
                            className="mb-2"
                        )
                    ], width=4),
                    dbc.Col([
                        html.Label("Site Filter", className="form-label"),
                        dcc.Dropdown(
                            id='wafer-site-filter',
                            placeholder="All Sites",
                            clearable=True,
                            className="mb-2"
                        )
                    ], width=3),
                    dbc.Col([
                        html.Label("Product Filter", className="form-label"),
                        dcc.Dropdown(
                            id='wafer-product-filter',
                            placeholder="All Products",
                            clearable=True,
                            className="mb-2"
                        )
                    ], width=3),
                    dbc.Col([
                        html.Label("Time Range", className="form-label"),
                        dcc.Dropdown(
                            id='wafer-time-filter',
                            options=[
                                {'label': 'Last 7 days', 'value': 7},
                                {'label': 'Last 30 days', 'value': 30},
                                {'label': 'Last 90 days', 'value': 90}
                            ],
                            value=30,
                            clearable=False,
                            className="mb-2"
                        )
                    ], width=2)
                ])
            ])
        ], className="mb-4"),

        # Wafer map display section
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Wafer Map Visualization", className="mb-0"),
                        html.Small("Die-level yield visualization with color-coded performance", className="text-muted")
                    ]),
                    dbc.CardBody([
                        html.Div(
                            id="wafer-map-display",
                            style={'minHeight': '500px'},
                            className="d-flex align-items-center justify-content-center"
                        )
                    ])
                ])
            ], width=8),
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Wafer Statistics", className="mb-0")
                    ]),
                    dbc.CardBody([
                        html.Div(id="wafer-stats-display")
                    ])
                ], className="mb-3"),
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Bin Distribution", className="mb-0")
                    ]),
                    dbc.CardBody([
                        dcc.Graph(
                            id='wafer-bin-chart',
                            config={'displayModeBar': False, 'displaylogo': False},
                            style={'height': '250px'}
                        )
                    ])
                ])
            ], width=4)
        ], className="mb-4"),

        # Wafer comparison section
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Wafer Yield Comparison", className="mb-0"),
                        html.Small("Compare yield across wafers in the selected lot", className="text-muted")
                    ]),
                    dbc.CardBody([
                        dcc.Graph(
                            id='wafer-comparison-chart',
                            config={'displayModeBar': True, 'displaylogo': False},
                            style={'height': '350px'}
                        )
                    ])
                ])
            ], width=6),
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Die Position Analysis", className="mb-0"),
                        html.Small("Yield patterns by die position", className="text-muted")
                    ]),
                    dbc.CardBody([
                        dcc.Graph(
                            id='die-position-chart',
                            config={'displayModeBar': True, 'displaylogo': False},
                            style={'height': '350px'}
                        )
                    ])
                ])
            ], width=6)
        ], className="mb-4"),

        # Detailed wafer information table
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Wafer Details", className="mb-0"),
                        html.Small("Detailed information for all wafers in the selected lot", className="text-muted")
                    ]),
                    dbc.CardBody([
                        html.Div(id="wafer-details-table")
                    ])
                ])
            ])
        ])

    ], fluid=True)
