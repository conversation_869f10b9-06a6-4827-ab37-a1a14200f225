"""
Chart components for the dashboard
"""

import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from typing import List, Dict, Any
from dash import html, dcc
import dash_bootstrap_components as dbc


class YieldCharts:
    """Class for creating yield-related charts"""

    def __init__(self):
        self.color_palette = {
            'primary': '#0d6efd',
            'success': '#198754',
            'danger': '#dc3545',
            'warning': '#ffc107',
            'info': '#0dcaf0',
            'secondary': '#6c757d'
        }

    def create_yield_trend_chart(self, trend_data: List[Dict[str, Any]]) -> go.Figure:
        """Create yield trend line chart"""
        if not trend_data:
            return self.create_empty_chart("No trend data available")

        df = pd.DataFrame(trend_data)
        df['period'] = pd.to_datetime(df['period'])

        fig = go.Figure()

        # Add yield trend line
        fig.add_trace(go.Scatter(
            x=df['period'],
            y=df['average_yield'],
            mode='lines+markers',
            name='Average Yield',
            line=dict(color=self.color_palette['primary'], width=3),
            marker=dict(size=8),
            hovertemplate='<b>%{x}</b><br>Yield: %{y:.1f}%<br>Lots: %{customdata}<extra></extra>',
            customdata=df['lot_count']
        ))

        # Add overall yield line if different from average
        if 'overall_yield' in df.columns:
            fig.add_trace(go.Scatter(
                x=df['period'],
                y=df['overall_yield'],
                mode='lines',
                name='Overall Yield',
                line=dict(color=self.color_palette['success'], width=2, dash='dash'),
                hovertemplate='<b>%{x}</b><br>Overall Yield: %{y:.1f}%<extra></extra>'
            ))

        # Calculate trend line
        if len(df) > 1:
            x_numeric = np.arange(len(df))
            z = np.polyfit(x_numeric, df['average_yield'], 1)
            trend_line = np.poly1d(z)(x_numeric)

            fig.add_trace(go.Scatter(
                x=df['period'],
                y=trend_line,
                mode='lines',
                name='Trend',
                line=dict(color=self.color_palette['warning'], width=2, dash='dot'),
                hovertemplate='Trend: %{y:.1f}%<extra></extra>'
            ))

        fig.update_layout(
            title="Yield Trend Over Time",
            xaxis_title="Date",
            yaxis_title="Yield (%)",
            hovermode='x unified',
            showlegend=True,
            template='plotly_white',
            height=400
        )

        return fig

    def create_site_performance_chart(self, site_data: List[Dict[str, Any]]) -> go.Figure:
        """Create site performance bar chart"""
        if not site_data:
            return self.create_empty_chart("No site performance data available")

        df = pd.DataFrame(site_data)

        fig = go.Figure(data=[
            go.Bar(
                x=df['site_code'],
                y=df['average_yield'],
                text=[f"{y:.1f}%" for y in df['average_yield']],
                textposition='auto',
                marker_color=self.color_palette['info'],
                hovertemplate='<b>%{x}</b><br>Yield: %{y:.1f}%<br>Lots: %{customdata}<extra></extra>',
                customdata=df['lot_count']
            )
        ])

        fig.update_layout(
            title="Site Performance Comparison",
            xaxis_title="Site",
            yaxis_title="Average Yield (%)",
            template='plotly_white',
            height=400
        )

        return fig

    def create_top_products_chart(self, products_data: List[Dict[str, Any]]) -> go.Figure:
        """Create top products horizontal bar chart"""
        if not products_data:
            return self.create_empty_chart("No product data available")

        df = pd.DataFrame(products_data)

        fig = go.Figure(data=[
            go.Bar(
                y=df['product_name'],
                x=df['lot_count'],
                orientation='h',
                text=[f"{count} lots" for count in df['lot_count']],
                textposition='auto',
                marker_color=self.color_palette['success'],
                hovertemplate='<b>%{y}</b><br>Lots: %{x}<br>Avg Yield: %{customdata:.1f}%<extra></extra>',
                customdata=df['average_yield']
            )
        ])

        fig.update_layout(
            title="Top Products by Volume",
            xaxis_title="Lot Count",
            yaxis_title="Product",
            template='plotly_white',
            height=350
        )

        return fig

    def create_pareto_chart(self, pareto_data: List[Dict[str, Any]]) -> go.Figure:
        """Create Pareto chart for failure analysis"""
        if not pareto_data:
            return self.create_empty_chart("No failure data available")

        df = pd.DataFrame(pareto_data)

        fig = make_subplots(specs=[[{"secondary_y": True}]])

        # Add bar chart for failure counts
        fig.add_trace(
            go.Bar(
                x=df['bin_number'],
                y=df['count'],
                name='Failure Count',
                marker_color=self.color_palette['danger'],
                yaxis='y',
                hovertemplate='Bin %{x}<br>Count: %{y}<br>Percentage: %{customdata:.1f}%<extra></extra>',
                customdata=df['percentage']
            ),
            secondary_y=False
        )

        # Add line chart for cumulative percentage
        fig.add_trace(
            go.Scatter(
                x=df['bin_number'],
                y=df['cumulative_percentage'],
                mode='lines+markers',
                name='Cumulative %',
                line=dict(color=self.color_palette['warning'], width=3),
                marker=dict(size=8),
                yaxis='y2',
                hovertemplate='Bin %{x}<br>Cumulative: %{y:.1f}%<extra></extra>'
            ),
            secondary_y=True
        )

        # Add 80% line
        fig.add_hline(y=80, line_dash="dash", line_color="gray",
                     annotation_text="80%", secondary_y=True)

        fig.update_xaxes(title_text="Failure Bin")
        fig.update_yaxes(title_text="Failure Count", secondary_y=False)
        fig.update_yaxes(title_text="Cumulative Percentage (%)", secondary_y=True)

        fig.update_layout(
            title="Failure Mode Pareto Analysis",
            template='plotly_white',
            height=350
        )

        return fig

    def create_wafer_map(self, wafer_data: Dict[str, Any]) -> html.Div:
        """Create interactive wafer map visualization"""
        if not wafer_data or not wafer_data.get('wafers'):
            return html.Div("No wafer data available", className="text-center text-muted p-4")

        wafer_components = []

        for i, wafer in enumerate(wafer_data['wafers']):
            # Create wafer map heatmap
            if wafer.get('die_map'):
                die_map = np.array(wafer['die_map'])

                fig = go.Figure(data=go.Heatmap(
                    z=die_map,
                    colorscale=[
                        [0, '#dc3545'],    # Red for failed die
                        [0.5, '#ffc107'],  # Yellow for marginal
                        [1, '#198754']     # Green for good die
                    ],
                    showscale=True,
                    colorbar=dict(
                        title="Die Status",
                        tickvals=[0, 0.5, 1],
                        ticktext=['Fail', 'Marginal', 'Pass']
                    ),
                    hovertemplate='X: %{x}<br>Y: %{y}<br>Status: %{z}<extra></extra>'
                ))

                fig.update_layout(
                    title=f"Wafer {wafer['wafer_id']} - Yield: {wafer['wafer_yield']:.1f}%",
                    xaxis_title="Die X Position",
                    yaxis_title="Die Y Position",
                    template='plotly_white',
                    height=400,
                    width=500
                )

                wafer_component = dbc.Col([
                    dcc.Graph(
                        figure=fig,
                        config={'displayModeBar': True, 'displaylogo': False}
                    )
                ], width=6 if len(wafer_data['wafers']) > 1 else 12)

                wafer_components.append(wafer_component)

        return dbc.Row(wafer_components)

    def create_control_limits_chart(self, control_data: Dict[str, Any]) -> go.Figure:
        """Create control limits chart with statistical boundaries"""
        if not control_data or not control_data.get('time_series'):
            return self.create_empty_chart("No control limits data available")

        time_series = control_data['time_series']
        control_limits = control_data['control_limits']

        df = pd.DataFrame(time_series)
        df['timestamp'] = pd.to_datetime(df['timestamp'])

        fig = go.Figure()

        # Add control limits
        ucl = control_limits['upper_control_limit']
        lcl = control_limits['lower_control_limit']
        uwl = control_limits['upper_warning_limit']
        lwl = control_limits['lower_warning_limit']
        mean = control_limits['mean']

        # Add limit lines
        fig.add_hline(y=ucl, line_dash="dash", line_color="red",
                     annotation_text="UCL", annotation_position="bottom right")
        fig.add_hline(y=uwl, line_dash="dash", line_color="orange",
                     annotation_text="UWL", annotation_position="bottom right")
        fig.add_hline(y=mean, line_dash="solid", line_color="blue",
                     annotation_text="Mean", annotation_position="bottom right")
        fig.add_hline(y=lwl, line_dash="dash", line_color="orange",
                     annotation_text="LWL", annotation_position="top right")
        fig.add_hline(y=lcl, line_dash="dash", line_color="red",
                     annotation_text="LCL", annotation_position="top right")

        # Add data points
        colors = []
        for value in df['value']:
            if value > ucl or value < lcl:
                colors.append('red')  # Out of control
            elif value > uwl or value < lwl:
                colors.append('orange')  # Warning
            else:
                colors.append('green')  # In control

        fig.add_trace(go.Scatter(
            x=df['timestamp'],
            y=df['value'],
            mode='lines+markers',
            name='Test Values',
            line=dict(color='blue', width=2),
            marker=dict(color=colors, size=8),
            hovertemplate='<b>%{x}</b><br>Value: %{y:.3f}<extra></extra>'
        ))

        fig.update_layout(
            title=f"Control Chart - {control_data['test_name']}",
            xaxis_title="Time",
            yaxis_title="Test Value",
            template='plotly_white',
            height=400,
            showlegend=False
        )

        return fig

    def create_empty_chart(self, message: str) -> go.Figure:
        """Create empty chart with message"""
        fig = go.Figure()
        fig.add_annotation(
            text=message,
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            xanchor='center', yanchor='middle',
            showarrow=False,
            font=dict(size=16, color="gray")
        )
        fig.update_layout(
            template='plotly_white',
            xaxis=dict(showgrid=False, showticklabels=False, zeroline=False),
            yaxis=dict(showgrid=False, showticklabels=False, zeroline=False),
            height=400
        )
        return fig
