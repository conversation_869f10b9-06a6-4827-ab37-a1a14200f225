"""
Report generation service for automated yield reports
"""

import os
import io
import tempfile
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from pathlib import Path
import hashlib

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from sqlalchemy.orm import Session
import structlog

from app.models.yield_data import YieldData, TestResult
from app.models.report import Report, ReportTemplate, ReportFormat, ReportType
from app.models.site import Site
from app.services.statistical_analyzer import StatisticalAnalyzer
from app.config import settings

logger = structlog.get_logger()


class ReportGenerator:
    """Service for generating automated yield reports"""

    def __init__(self, db: Session):
        self.db = db
        self.analyzer = StatisticalAnalyzer()
        self.reports_dir = Path(settings.REPORTS_DIR)
        self.reports_dir.mkdir(exist_ok=True)

        # Set up matplotlib for report charts
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")

    async def generate_report(self, report_id: int) -> Dict[str, Any]:
        """
        Generate report based on report configuration
        """
        logger.info("Starting report generation", report_id=report_id)

        # Get report configuration
        report = self.db.query(Report).filter(Report.id == report_id).first()
        if not report:
            raise ValueError(f"Report {report_id} not found")

        try:
            # Update report status
            report.status = "generating"
            self.db.commit()

            # Get data for report
            report_data = await self._collect_report_data(report)

            # Generate report based on format
            if report.report_format == ReportFormat.PDF:
                file_path = await self._generate_pdf_report(report, report_data)
            elif report.report_format == ReportFormat.DOCX:
                file_path = await self._generate_docx_report(report, report_data)
            elif report.report_format == ReportFormat.HTML:
                file_path = await self._generate_html_report(report, report_data)
            elif report.report_format == ReportFormat.XLSX:
                file_path = await self._generate_excel_report(report, report_data)
            else:
                raise ValueError(f"Unsupported report format: {report.report_format}")

            # Calculate file hash and size
            file_hash = self._calculate_file_hash(file_path)
            file_size = os.path.getsize(file_path)

            # Update report record
            report.file_path = str(file_path)
            report.file_hash = file_hash
            report.file_size = file_size
            report.generated_at = datetime.utcnow()
            report.status = "completed"
            report.total_lots = report_data.get('total_lots', 0)
            report.total_wafers = report_data.get('total_wafers', 0)
            report.average_yield = report_data.get('average_yield', 0.0)

            self.db.commit()

            logger.info(
                "Report generation completed",
                report_id=report_id,
                file_path=file_path,
                file_size=file_size
            )

            return {
                'report_id': report_id,
                'file_path': str(file_path),
                'file_size': file_size,
                'file_hash': file_hash,
                'status': 'completed'
            }

        except Exception as e:
            # Update report with error
            report.status = "failed"
            report.error_message = str(e)
            report.retry_count = (report.retry_count or 0) + 1
            self.db.commit()

            logger.error(
                "Report generation failed",
                report_id=report_id,
                error=str(e)
            )
            raise

    async def _collect_report_data(self, report: Report) -> Dict[str, Any]:
        """
        Collect data needed for report generation
        """
        # Base query for yield data
        query = self.db.query(YieldData).filter(
            YieldData.test_start_time >= report.period_start,
            YieldData.test_start_time <= report.period_end
        )

        # Apply filters
        if report.site_ids:
            query = query.filter(YieldData.site_id.in_(report.site_ids))

        if report.product_names:
            query = query.filter(YieldData.product_name.in_(report.product_names))

        # Get yield data
        yield_data = query.all()

        # Calculate summary statistics
        total_lots = len(yield_data)
        total_wafers = sum(yd.wafer_count or 0 for yd in yield_data)
        total_die = sum(yd.total_die_count or 0 for yd in yield_data)
        good_die = sum(yd.good_die_count or 0 for yd in yield_data)

        average_yield = np.mean([yd.yield_percent for yd in yield_data]) if yield_data else 0.0
        overall_yield = (good_die / total_die * 100) if total_die > 0 else 0.0

        # Get site information
        sites_data = {}
        if report.site_ids:
            sites = self.db.query(Site).filter(Site.id.in_(report.site_ids)).all()
            sites_data = {site.id: site for site in sites}

        # Calculate trends
        yield_trends = self._calculate_yield_trends(yield_data)

        # Get anomalies
        anomalies = [yd for yd in yield_data if yd.has_anomalies]

        # Product analysis
        product_analysis = self._analyze_by_product(yield_data)

        # Site analysis
        site_analysis = self._analyze_by_site(yield_data, sites_data)

        return {
            'report': report,
            'yield_data': yield_data,
            'total_lots': total_lots,
            'total_wafers': total_wafers,
            'total_die': total_die,
            'good_die': good_die,
            'average_yield': average_yield,
            'overall_yield': overall_yield,
            'yield_trends': yield_trends,
            'anomalies': anomalies,
            'product_analysis': product_analysis,
            'site_analysis': site_analysis,
            'sites_data': sites_data,
            'period_start': report.period_start,
            'period_end': report.period_end
        }

    async def _generate_pdf_report(self, report: Report, data: Dict[str, Any]) -> Path:
        """
        Generate PDF report using ReportLab
        """
        filename = f"yield_report_{report.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        file_path = self.reports_dir / filename

        # Create PDF document
        doc = SimpleDocTemplate(str(file_path), pagesize=A4)
        styles = getSampleStyleSheet()
        story = []

        # Title
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=1  # Center alignment
        )
        story.append(Paragraph(report.title, title_style))
        story.append(Spacer(1, 20))

        # Executive Summary
        story.append(Paragraph("Executive Summary", styles['Heading2']))
        summary_text = self._generate_executive_summary(data)
        story.append(Paragraph(summary_text, styles['Normal']))
        story.append(Spacer(1, 20))

        # Key Metrics Table
        story.append(Paragraph("Key Performance Indicators", styles['Heading2']))
        kpi_table = self._create_kpi_table(data)
        story.append(kpi_table)
        story.append(Spacer(1, 20))

        # Generate and embed charts
        charts_dir = self.reports_dir / "charts" / str(report.id)
        charts_dir.mkdir(parents=True, exist_ok=True)

        # Yield trend chart
        trend_chart_path = await self._create_yield_trend_chart(data, charts_dir)
        if trend_chart_path.exists():
            story.append(Paragraph("Yield Trend Analysis", styles['Heading2']))
            story.append(Image(str(trend_chart_path), width=6*inch, height=4*inch))
            story.append(Spacer(1, 20))

        # Site performance chart
        site_chart_path = await self._create_site_performance_chart(data, charts_dir)
        if site_chart_path.exists():
            story.append(Paragraph("Site Performance Comparison", styles['Heading2']))
            story.append(Image(str(site_chart_path), width=6*inch, height=4*inch))
            story.append(Spacer(1, 20))

        # Product analysis
        if data['product_analysis']:
            story.append(Paragraph("Product Analysis", styles['Heading2']))
            product_table = self._create_product_analysis_table(data['product_analysis'])
            story.append(product_table)
            story.append(Spacer(1, 20))

        # Anomalies section
        if data['anomalies']:
            story.append(Paragraph("Anomalies and Outliers", styles['Heading2']))
            anomalies_text = self._generate_anomalies_summary(data['anomalies'])
            story.append(Paragraph(anomalies_text, styles['Normal']))
            story.append(Spacer(1, 20))

        # Recommendations
        story.append(Paragraph("Recommendations", styles['Heading2']))
        recommendations = self._generate_recommendations(data)
        story.append(Paragraph(recommendations, styles['Normal']))

        # Build PDF
        doc.build(story)

        return file_path

    async def _generate_docx_report(self, report: Report, data: Dict[str, Any]) -> Path:
        """
        Generate DOCX report using python-docx
        """
        filename = f"yield_report_{report.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
        file_path = self.reports_dir / filename

        # Create document
        doc = Document()

        # Title
        title = doc.add_heading(report.title, 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # Executive Summary
        doc.add_heading('Executive Summary', level=1)
        summary_text = self._generate_executive_summary(data)
        doc.add_paragraph(summary_text)

        # Key Metrics
        doc.add_heading('Key Performance Indicators', level=1)
        self._add_kpi_table_to_docx(doc, data)

        # Generate charts
        charts_dir = self.reports_dir / "charts" / str(report.id)
        charts_dir.mkdir(parents=True, exist_ok=True)

        # Add yield trend chart
        trend_chart_path = await self._create_yield_trend_chart(data, charts_dir)
        if trend_chart_path.exists():
            doc.add_heading('Yield Trend Analysis', level=1)
            doc.add_picture(str(trend_chart_path), width=Inches(6))

        # Add site performance chart
        site_chart_path = await self._create_site_performance_chart(data, charts_dir)
        if site_chart_path.exists():
            doc.add_heading('Site Performance Comparison', level=1)
            doc.add_picture(str(site_chart_path), width=Inches(6))

        # Product analysis
        if data['product_analysis']:
            doc.add_heading('Product Analysis', level=1)
            self._add_product_analysis_to_docx(doc, data['product_analysis'])

        # Anomalies
        if data['anomalies']:
            doc.add_heading('Anomalies and Outliers', level=1)
            anomalies_text = self._generate_anomalies_summary(data['anomalies'])
            doc.add_paragraph(anomalies_text)

        # Recommendations
        doc.add_heading('Recommendations', level=1)
        recommendations = self._generate_recommendations(data)
        doc.add_paragraph(recommendations)

        # Save document
        doc.save(str(file_path))

        return file_path

    def _generate_executive_summary(self, data: Dict[str, Any]) -> str:
        """
        Generate natural language executive summary
        """
        report = data['report']
        total_lots = data['total_lots']
        average_yield = data['average_yield']
        overall_yield = data['overall_yield']
        anomalies_count = len(data['anomalies'])

        # Determine period description
        period_days = (data['period_end'] - data['period_start']).days
        period_desc = f"{period_days} days" if period_days < 30 else f"{period_days // 30} months"

        # Yield assessment
        if average_yield >= 90:
            yield_assessment = "excellent"
        elif average_yield >= 80:
            yield_assessment = "good"
        elif average_yield >= 70:
            yield_assessment = "acceptable"
        else:
            yield_assessment = "concerning"

        # Trend analysis
        trend_info = data['yield_trends']
        trend_desc = ""
        if trend_info.get('trend') == 'improving':
            trend_desc = "showing positive improvement"
        elif trend_info.get('trend') == 'declining':
            trend_desc = "showing declining performance"
        else:
            trend_desc = "remaining stable"

        summary = f"""
        This report analyzes yield performance over a {period_desc} period from {data['period_start'].strftime('%Y-%m-%d')}
        to {data['period_end'].strftime('%Y-%m-%d')}. During this period, {total_lots} lots were processed with an
        average yield of {average_yield:.1f}% and overall yield of {overall_yield:.1f}%.

        The yield performance is considered {yield_assessment}, {trend_desc} over the reporting period.
        {anomalies_count} anomalies were detected, requiring attention for process optimization.

        Key findings include performance variations across sites and products, with detailed analysis provided
        in the following sections. Recommendations for yield improvement are included at the end of this report.
        """

        return summary.strip()

    def _create_kpi_table(self, data: Dict[str, Any]) -> Table:
        """
        Create KPI table for PDF report
        """
        kpi_data = [
            ['Metric', 'Value'],
            ['Total Lots', str(data['total_lots'])],
            ['Total Wafers', str(data['total_wafers'])],
            ['Total Die', f"{data['total_die']:,}"],
            ['Good Die', f"{data['good_die']:,}"],
            ['Average Yield', f"{data['average_yield']:.1f}%"],
            ['Overall Yield', f"{data['overall_yield']:.1f}%"],
            ['Anomalies Detected', str(len(data['anomalies']))],
            ['Yield Trend', data['yield_trends'].get('trend', 'Unknown').title()]
        ]

        table = Table(kpi_data, colWidths=[3*inch, 2*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        return table

    async def _create_yield_trend_chart(self, data: Dict[str, Any], charts_dir: Path) -> Path:
        """
        Create yield trend chart for report
        """
        chart_path = charts_dir / "yield_trend.png"

        if not data['yield_data']:
            return chart_path

        # Prepare data
        df = pd.DataFrame([
            {
                'date': yd.test_start_time.date(),
                'yield': yd.yield_percent,
                'lot_id': yd.lot_id
            }
            for yd in data['yield_data']
        ])

        # Group by date and calculate daily averages
        daily_yield = df.groupby('date')['yield'].agg(['mean', 'count']).reset_index()

        # Create plot
        plt.figure(figsize=(10, 6))
        plt.plot(daily_yield['date'], daily_yield['mean'], marker='o', linewidth=2, markersize=6)
        plt.title('Daily Yield Trend', fontsize=16, fontweight='bold')
        plt.xlabel('Date', fontsize=12)
        plt.ylabel('Yield (%)', fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()

        # Add trend line
        if len(daily_yield) > 1:
            x_numeric = np.arange(len(daily_yield))
            z = np.polyfit(x_numeric, daily_yield['mean'], 1)
            trend_line = np.poly1d(z)(x_numeric)
            plt.plot(daily_yield['date'], trend_line, '--', color='red', alpha=0.7, label='Trend')
            plt.legend()

        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()

        return chart_path

    async def _create_site_performance_chart(self, data: Dict[str, Any], charts_dir: Path) -> Path:
        """
        Create site performance chart for report
        """
        chart_path = charts_dir / "site_performance.png"

        if not data['site_analysis']:
            return chart_path

        # Prepare data
        sites = list(data['site_analysis'].keys())
        yields = [data['site_analysis'][site]['average_yield'] for site in sites]
        lot_counts = [data['site_analysis'][site]['lot_count'] for site in sites]

        # Create plot
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

        # Yield comparison
        bars1 = ax1.bar(sites, yields, color='skyblue', alpha=0.7)
        ax1.set_title('Average Yield by Site', fontweight='bold')
        ax1.set_ylabel('Yield (%)')
        ax1.set_xlabel('Site')

        # Add value labels on bars
        for bar, yield_val in zip(bars1, yields):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    f'{yield_val:.1f}%', ha='center', va='bottom')

        # Lot count comparison
        bars2 = ax2.bar(sites, lot_counts, color='lightcoral', alpha=0.7)
        ax2.set_title('Lot Count by Site', fontweight='bold')
        ax2.set_ylabel('Number of Lots')
        ax2.set_xlabel('Site')

        # Add value labels on bars
        for bar, count in zip(bars2, lot_counts):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    str(count), ha='center', va='bottom')

        plt.tight_layout()
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()

        return chart_path
