"""
Celery tasks for data processing
"""

from typing import Dict, Any, List
from datetime import datetime
import structlog

from celery import Celery
from sqlalchemy.orm import Session

from app.database import get_db
from app.models.yield_data import YieldData, DataSource
from app.models.user import User
from app.services.data_processor import DataProcessor
from app.services.statistical_analyzer import StatisticalAnalyzer
from app.config import settings

# Configure logging
logger = structlog.get_logger()

# Initialize Celery app
celery_app = Celery(
    'yielddoc',
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=['app.tasks.data_processing', 'app.tasks.report_generation', 'app.tasks.maintenance']
)

# Configure Celery
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)


@celery_app.task(bind=True, name='process_stdf_file')
def process_stdf_file_task(self, file_path: str, site_id: int, user_id: int, original_filename: str) -> Dict[str, Any]:
    """
    Background task to process STDF file
    """
    logger.info(
        "Starting STDF file processing task",
        task_id=self.request.id,
        file_path=file_path,
        site_id=site_id,
        user_id=user_id
    )

    try:
        # Update task progress
        self.update_state(state='PROGRESS', meta={'progress': 10, 'status': 'Initializing processor'})

        # Get database session
        db = next(get_db())

        # Initialize data processor
        processor = DataProcessor(db)

        # Update progress
        self.update_state(state='PROGRESS', meta={'progress': 20, 'status': 'Parsing STDF file'})

        # Process file (simulate async call)
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # Create a mock UploadFile object
        class MockUploadFile:
            def __init__(self, file_path: str, filename: str):
                self.filename = filename
                self._file_path = file_path

            async def read(self):
                with open(self._file_path, 'rb') as f:
                    return f.read()

        mock_file = MockUploadFile(file_path, original_filename)

        # Update progress
        self.update_state(state='PROGRESS', meta={'progress': 50, 'status': 'Processing yield data'})

        # Process the file
        result = loop.run_until_complete(
            processor.process_stdf_file(mock_file, site_id, user_id)
        )

        # Update progress
        self.update_state(state='PROGRESS', meta={'progress': 80, 'status': 'Running statistical analysis'})

        # Run statistical analysis on processed data
        if result.get('yield_data_ids'):
            analyzer = StatisticalAnalyzer()
            for yield_data_id in result['yield_data_ids']:
                yield_data = db.query(YieldData).filter(YieldData.id == yield_data_id).first()
                if yield_data:
                    # Detect anomalies
                    test_values = [tr.test_value for tr in yield_data.test_results if tr.test_value is not None]
                    if test_values:
                        anomaly_result = analyzer.detect_anomalies(test_values)
                        yield_data.has_anomalies = anomaly_result['anomaly_count'] > 0
                        yield_data.quality_score = 1.0 - (anomaly_result['anomaly_rate'] / 100)

            db.commit()

        # Update progress
        self.update_state(state='PROGRESS', meta={'progress': 100, 'status': 'Processing completed'})

        # Clean up temporary file
        import os
        if os.path.exists(file_path):
            os.remove(file_path)

        logger.info(
            "STDF file processing task completed",
            task_id=self.request.id,
            lots_processed=result.get('lots_processed', 0)
        )

        return {
            'status': 'completed',
            'lots_processed': result.get('lots_processed', 0),
            'yield_data_ids': result.get('yield_data_ids', []),
            'message': f"Successfully processed {result.get('lots_processed', 0)} lots"
        }

    except Exception as e:
        logger.error(
            "STDF file processing task failed",
            task_id=self.request.id,
            error=str(e)
        )

        # Clean up temporary file on error
        import os
        if os.path.exists(file_path):
            os.remove(file_path)

        self.update_state(
            state='FAILURE',
            meta={'error': str(e), 'status': 'Processing failed'}
        )

        raise


@celery_app.task(bind=True, name='process_csv_file')
def process_csv_file_task(self, file_content: str, site_id: int, user_id: int, original_filename: str) -> Dict[str, Any]:
    """
    Background task to process CSV file
    """
    logger.info(
        "Starting CSV file processing task",
        task_id=self.request.id,
        site_id=site_id,
        user_id=user_id,
        filename=original_filename
    )

    try:
        # Update task progress
        self.update_state(state='PROGRESS', meta={'progress': 10, 'status': 'Initializing processor'})

        # Get database session
        db = next(get_db())

        # Initialize data processor
        processor = DataProcessor(db)

        # Update progress
        self.update_state(state='PROGRESS', meta={'progress': 30, 'status': 'Parsing CSV data'})

        # Create a mock UploadFile object
        class MockUploadFile:
            def __init__(self, content: str, filename: str):
                self.filename = filename
                self._content = content.encode('utf-8')

            async def read(self):
                return self._content

        mock_file = MockUploadFile(file_content, original_filename)

        # Update progress
        self.update_state(state='PROGRESS', meta={'progress': 60, 'status': 'Processing yield data'})

        # Process the file
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        result = loop.run_until_complete(
            processor.process_csv_file(mock_file, site_id, user_id)
        )

        # Update progress
        self.update_state(state='PROGRESS', meta={'progress': 90, 'status': 'Finalizing'})

        # Update progress
        self.update_state(state='PROGRESS', meta={'progress': 100, 'status': 'Processing completed'})

        logger.info(
            "CSV file processing task completed",
            task_id=self.request.id,
            records_processed=result.get('records_processed', 0)
        )

        return {
            'status': 'completed',
            'records_processed': result.get('records_processed', 0),
            'yield_data_ids': result.get('yield_data_ids', []),
            'message': f"Successfully processed {result.get('records_processed', 0)} records"
        }

    except Exception as e:
        logger.error(
            "CSV file processing task failed",
            task_id=self.request.id,
            error=str(e)
        )

        self.update_state(
            state='FAILURE',
            meta={'error': str(e), 'status': 'Processing failed'}
        )

        raise
