"""
Main Dashboard Layout
"""

from dash import dcc, html
import dash_bootstrap_components as dbc


def create_main_layout():
    """Create the main dashboard layout"""
    return dbc.Container([
        # Header section
        dbc.Row([
            dbc.Col([
                html.H1("Yield Dashboard", className="display-4 text-primary mb-0"),
                html.P("Real-time semiconductor yield monitoring and analysis",
                      className="lead text-muted")
            ], width=8),
            dbc.Col([
                html.Div([
                    html.P("Last Updated", className="mb-1 text-muted small"),
                    html.P(id="last-updated", className="mb-0 fw-bold")
                ], className="text-end")
            ], width=4)
        ], className="mb-4"),

        # Filters section
        dbc.Card([
            dbc.CardBody([
                dbc.Row([
                    dbc.Col([
                        html.Label("Site Filter", className="form-label"),
                        dcc.Dropdown(
                            id='site-filter',
                            placeholder="All Sites",
                            clearable=True,
                            className="mb-2"
                        )
                    ], width=3),
                    dbc.Col([
                        html.Label("Time Period", className="form-label"),
                        dcc.Dropdown(
                            id='days-filter',
                            options=[
                                {'label': 'Last 7 days', 'value': 7},
                                {'label': 'Last 30 days', 'value': 30},
                                {'label': 'Last 90 days', 'value': 90},
                                {'label': 'Last 180 days', 'value': 180}
                            ],
                            value=30,
                            clearable=False,
                            className="mb-2"
                        )
                    ], width=3),
                    dbc.Col([
                        html.Label("Granularity", className="form-label"),
                        dcc.Dropdown(
                            id='granularity-filter',
                            options=[
                                {'label': 'Daily', 'value': 'daily'},
                                {'label': 'Weekly', 'value': 'weekly'},
                                {'label': 'Monthly', 'value': 'monthly'}
                            ],
                            value='daily',
                            clearable=False,
                            className="mb-2"
                        )
                    ], width=3),
                    dbc.Col([
                        html.Label("Auto Refresh", className="form-label"),
                        dbc.Switch(
                            id="auto-refresh-switch",
                            label="Enabled",
                            value=True,
                            className="mt-2"
                        )
                    ], width=3)
                ])
            ])
        ], className="mb-4"),

        # KPI Cards
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.Div([
                            html.I(className="fas fa-microchip fa-2x text-primary mb-2"),
                            html.H3(id="total-lots-card", className="mb-0"),
                            html.P("Total Lots", className="text-muted mb-0")
                        ], className="text-center")
                    ])
                ], className="h-100")
            ], width=3),
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.Div([
                            html.I(className="fas fa-chart-line fa-2x text-success mb-2"),
                            html.Div(id="average-yield-card"),
                            html.P("Average Yield", className="text-muted mb-0")
                        ], className="text-center")
                    ])
                ], className="h-100")
            ], width=3),
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.Div([
                            html.I(className="fas fa-trending-up fa-2x text-info mb-2"),
                            html.H3(id="yield-trend-card", className="mb-0"),
                            html.P("Yield Trend", className="text-muted mb-0")
                        ], className="text-center")
                    ])
                ], className="h-100")
            ], width=3),
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.Div([
                            html.I(className="fas fa-exclamation-triangle fa-2x text-warning mb-2"),
                            html.H3(id="anomalies-card", className="mb-0"),
                            html.P("Recent Anomalies", className="text-muted mb-0")
                        ], className="text-center")
                    ])
                ], className="h-100")
            ], width=3)
        ], className="mb-4"),

        # Charts section
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Yield Trend Analysis", className="mb-0"),
                        html.Small("Daily yield performance over time", className="text-muted")
                    ]),
                    dbc.CardBody([
                        dcc.Graph(
                            id='yield-trend-chart',
                            config={'displayModeBar': True, 'displaylogo': False},
                            style={'height': '400px'}
                        )
                    ])
                ])
            ], width=8),
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Site Performance", className="mb-0"),
                        html.Small("Yield comparison by site", className="text-muted")
                    ]),
                    dbc.CardBody([
                        dcc.Graph(
                            id='site-performance-chart',
                            config={'displayModeBar': True, 'displaylogo': False},
                            style={'height': '400px'}
                        )
                    ])
                ])
            ], width=4)
        ], className="mb-4"),

        # Second row of charts
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Top Products by Volume", className="mb-0"),
                        html.Small("Products with highest lot count", className="text-muted")
                    ]),
                    dbc.CardBody([
                        dcc.Graph(
                            id='top-products-chart',
                            config={'displayModeBar': True, 'displaylogo': False},
                            style={'height': '350px'}
                        )
                    ])
                ])
            ], width=6),
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Failure Mode Analysis", className="mb-0"),
                        html.Small("Pareto chart of failure bins", className="text-muted")
                    ]),
                    dbc.CardBody([
                        dcc.Graph(
                            id='pareto-chart',
                            config={'displayModeBar': True, 'displaylogo': False},
                            style={'height': '350px'}
                        )
                    ])
                ])
            ], width=6)
        ], className="mb-4"),

        # Recent Anomalies section
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Recent Anomalies", className="mb-0"),
                        html.Small("Lots with detected anomalies in the last 7 days", className="text-muted")
                    ]),
                    dbc.CardBody([
                        html.Div(id="anomalies-table")
                    ])
                ])
            ])
        ])

    ], fluid=True)
