"""
Celery tasks for report generation
"""

from typing import Dict, Any, List
from datetime import datetime, timedelta
import structlog

from celery import Celery
from celery.schedules import crontab
from sqlalchemy.orm import Session

from app.database import get_db
from app.models.report import Report, ReportStatus
from app.models.user import User
from app.services.report_generator import ReportGenerator
from app.services.email_service import EmailService
from app.config import settings

# Configure logging
logger = structlog.get_logger()

# Get Celery app from data_processing module
from app.tasks.data_processing import celery_app


@celery_app.task(bind=True, name='generate_report')
def generate_report_task(self, report_id: int) -> Dict[str, Any]:
    """
    Background task to generate report
    """
    logger.info(
        "Starting report generation task",
        task_id=self.request.id,
        report_id=report_id
    )

    try:
        # Update task progress
        self.update_state(state='PROGRESS', meta={'progress': 10, 'status': 'Initializing report generator'})

        # Get database session
        db = next(get_db())

        # Get report
        report = db.query(Report).filter(Report.id == report_id).first()
        if not report:
            raise ValueError(f"Report {report_id} not found")

        # Update report status
        report.status = ReportStatus.GENERATING
        db.commit()

        # Initialize report generator
        generator = ReportGenerator(db)

        # Update progress
        self.update_state(state='PROGRESS', meta={'progress': 30, 'status': 'Collecting data'})

        # Generate report
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        result = loop.run_until_complete(
            generator.generate_report(report_id)
        )

        # Update progress
        self.update_state(state='PROGRESS', meta={'progress': 80, 'status': 'Sending email notifications'})

        # Send email notifications if configured
        if report.email_recipients:
            email_service = EmailService()
            email_result = loop.run_until_complete(
                email_service.send_report_email(report_id)
            )
            result['email_sent'] = email_result.get('sent', False)

        # Update progress
        self.update_state(state='PROGRESS', meta={'progress': 100, 'status': 'Report generation completed'})

        logger.info(
            "Report generation task completed",
            task_id=self.request.id,
            report_id=report_id,
            file_path=result.get('file_path')
        )

        return {
            'status': 'completed',
            'report_id': report_id,
            'file_path': result.get('file_path'),
            'file_size': result.get('file_size'),
            'email_sent': result.get('email_sent', False),
            'message': 'Report generated successfully'
        }

    except Exception as e:
        logger.error(
            "Report generation task failed",
            task_id=self.request.id,
            report_id=report_id,
            error=str(e)
        )

        # Update report status
        db = next(get_db())
        report = db.query(Report).filter(Report.id == report_id).first()
        if report:
            report.status = ReportStatus.FAILED
            report.error_message = str(e)
            report.retry_count = (report.retry_count or 0) + 1
            db.commit()

        self.update_state(
            state='FAILURE',
            meta={'error': str(e), 'status': 'Report generation failed'}
        )

        raise


@celery_app.task(name='schedule_reports')
def schedule_reports_task() -> Dict[str, Any]:
    """
    Scheduled task to generate reports based on schedule
    """
    logger.info("Starting scheduled reports task")

    try:
        # Get database session
        db = next(get_db())

        # Find reports that need to be generated
        now = datetime.utcnow()
        scheduled_reports = db.query(Report).filter(
            Report.is_scheduled == True,
            Report.next_run <= now,
            Report.status.in_([ReportStatus.PENDING, ReportStatus.SCHEDULED])
        ).all()

        generated_reports = []

        for report in scheduled_reports:
            try:
                # Queue report generation
                task = generate_report_task.delay(report.id)

                # Update next run time
                if report.schedule_cron:
                    # Calculate next run time based on cron expression
                    # This is a simplified implementation
                    if 'daily' in report.schedule_cron.lower():
                        report.next_run = now + timedelta(days=1)
                    elif 'weekly' in report.schedule_cron.lower():
                        report.next_run = now + timedelta(weeks=1)
                    elif 'monthly' in report.schedule_cron.lower():
                        report.next_run = now + timedelta(days=30)
                    else:
                        report.next_run = now + timedelta(days=1)  # Default to daily

                report.status = ReportStatus.GENERATING
                generated_reports.append({
                    'report_id': report.id,
                    'task_id': task.id,
                    'next_run': report.next_run.isoformat()
                })

            except Exception as e:
                logger.error(
                    "Failed to schedule report",
                    report_id=report.id,
                    error=str(e)
                )
                report.status = ReportStatus.FAILED
                report.error_message = f"Scheduling failed: {str(e)}"

        db.commit()

        logger.info(
            "Scheduled reports task completed",
            reports_scheduled=len(generated_reports)
        )

        return {
            'status': 'completed',
            'reports_scheduled': len(generated_reports),
            'scheduled_reports': generated_reports
        }

    except Exception as e:
        logger.error(
            "Scheduled reports task failed",
            error=str(e)
        )
        raise


@celery_app.task(bind=True, name='retry_failed_report')
def retry_failed_report_task(self, report_id: int) -> Dict[str, Any]:
    """
    Retry failed report generation
    """
    logger.info(
        "Starting report retry task",
        task_id=self.request.id,
        report_id=report_id
    )

    try:
        # Get database session
        db = next(get_db())

        # Get report
        report = db.query(Report).filter(Report.id == report_id).first()
        if not report:
            raise ValueError(f"Report {report_id} not found")

        # Check if report can be retried
        max_retries = 3
        if (report.retry_count or 0) >= max_retries:
            raise ValueError(f"Report {report_id} has exceeded maximum retry attempts")

        # Reset report status
        report.status = ReportStatus.PENDING
        report.error_message = None
        db.commit()

        # Queue report generation
        task = generate_report_task.delay(report_id)

        logger.info(
            "Report retry task queued",
            task_id=self.request.id,
            report_id=report_id,
            generation_task_id=task.id
        )

        return {
            'status': 'queued',
            'report_id': report_id,
            'generation_task_id': task.id,
            'retry_count': report.retry_count or 0
        }

    except Exception as e:
        logger.error(
            "Report retry task failed",
            task_id=self.request.id,
            report_id=report_id,
            error=str(e)
        )
        raise


# Configure periodic tasks
celery_app.conf.beat_schedule = {
    'schedule-reports': {
        'task': 'schedule_reports',
        'schedule': crontab(minute=0),  # Run every hour
    },
    'cleanup-old-reports': {
        'task': 'cleanup_old_reports',
        'schedule': crontab(hour=2, minute=0),  # Run daily at 2 AM
    },
    'system-health-check': {
        'task': 'system_health_check',
        'schedule': crontab(minute='*/15'),  # Run every 15 minutes
    },
}
